@extends('layouts.index')

@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Tüm Müşteri Takip Kayıtları</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Takip Kayıtları</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('customers.index') }}" class="btn btn-secondary">Müşteri Listesi</a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            <form method="GET" action="{{ route('customer-followups.all') }}" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <select name="status" class="form-control">
                                        <option value="">Tüm Durumlar</option>
                                        @foreach(\App\Models\CustomerFollowup::$statusOptions as $value => $label)
                                            <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="input-group input-group-sm">
                                    <select name="conversation_type" class="form-control">
                                        <option value="">Tüm Görüşme Türleri</option>
                                        <option value="telefon" {{ request('conversation_type') == 'telefon' ? 'selected' : '' }}>Telefon</option>
                                        <option value="yerinde" {{ request('conversation_type') == 'yerinde' ? 'selected' : '' }}>Yerinde</option>
                                        <option value="bayide" {{ request('conversation_type') == 'bayide' ? 'selected' : '' }}>Bayide</option>
                                    </select>
                                </div>
                                <div class="input-group input-group-sm">
                                    <input type="date" name="date_from" class="form-control" placeholder="Başlangıç" value="{{ request('date_from') }}">
                                </div>
                                <div class="input-group input-group-sm">
                                    <input type="date" name="date_to" class="form-control" placeholder="Bitiş" value="{{ request('date_to') }}">
                                </div>
                                <div class="input-group input-group-sm">
                                    <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                </div>
                                @if(request('status') || request('conversation_type') || request('date_from') || request('date_to'))
                                    <a href="{{ route('customer-followups.all') }}" class="btn btn-outline-danger">Temizle</a>
                                @endif
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <table id="allFollowupsTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Müşteri</th>
                                <th>Takip Tarihi</th>
                                <th>Görüşme Tarihi</th>
                                <th>Durum</th>
                                <th>Görüşme Türü</th>
                                <th>Çalışma Türü</th>
                                <th>Ziyaret Eden</th>
                                <th>Şehir</th>
                                <th>İlçe</th>
                                <th>Not</th>
                                <th>Anlaşma</th>
                                <th>PlusCard Yükleme</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($followups as $followup)
                                <tr>
                                    <td>{{ $followup->id }}</td>
                                    <td>
                                        <a href="{{ route('customers.show', $followup->customer->id) }}" class="text-decoration-none">
                                            {{ $followup->customer->company_name ?? $followup->customer->name }}
                                        </a>
                                    </td>
                                    <td>{{ \Carbon\Carbon::parse($followup->track_date)->format('d.m.Y') }}</td>
                                    <td>{{ $followup->meet_date ? \Carbon\Carbon::parse($followup->meet_date)->format('d.m.Y') : '-' }}</td>
                                    <td>
                                        <span class="badge badge-{{ $followup->status == 'aktif' ? 'success' : ($followup->status == 'pasif' ? 'warning' : 'secondary') }}">
                                            {{ $followup->getStatusDisplayAttribute() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ ucfirst($followup->conversation_type) }}
                                        </span>
                                    </td>
                                    <td>{{ $followup->work_type ?? '-' }}</td>
                                    <td>{{ $followup->user_name ?? '-' }}</td>
                                    <td>{{ $followup->city ?? '-' }}</td>
                                    <td>{{ $followup->district ?? '-' }}</td>
                                    <td>{{ Str::limit($followup->note, 30) }}</td>
                                    <td>
                                        @if($followup->agreement_status === null)
                                            <span class="text-muted">-</span>
                                        @else
                                            <span class="badge badge-{{ $followup->agreement_status ? 'success' : 'danger' }}">
                                                {{ $followup->agreement_status ? 'Evet' : 'Hayır' }}
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($followup->pluscard_been_loaded === null)
                                            <span class="text-muted">-</span>
                                        @else
                                            <span class="badge badge-{{ $followup->pluscard_been_loaded ? 'success' : 'warning' }}">
                                                {{ $followup->pluscard_been_loaded ? 'Evet' : 'Hayır' }}
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('customers.customer-followups.show', [$followup->customer->id, $followup->id]) }}" class="btn btn-info btn-sm" title="Detay">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('customers.customer-followups.edit', [$followup->customer->id, $followup->id]) }}" class="btn btn-warning btn-sm" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('customers.customer-followups.destroy', [$followup->customer->id, $followup->id]) }}" method="POST" style="display:inline-block;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Silmek istediğinize emin misiniz?')" title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        {{ $followups->appends(request()->query())->links('pagination::bootstrap-4') }}
                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#allFollowupsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tümü"]],
        "order": [[0, "desc"]],
        "columnDefs": [
            {
                "targets": -1, // Son sütun (İşlemler)
                "orderable": false,
                "searchable": false
            }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#allFollowupsTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
@endpush