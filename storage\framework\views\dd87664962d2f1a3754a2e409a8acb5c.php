<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Tüm Müşteri Takip Kayıtları</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Takip Kayıtları</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-secondary">Müşteri Listesi</a>
                        </h3>

                        <div class="card-tools">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <form method="GET" action="<?php echo e(route('customer-followups.all')); ?>" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <select name="status" class="form-control">
                                        <option value="">Tüm Durumlar</option>
                                        <?php $__currentLoopData = \App\Models\CustomerFollowup::$statusOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($value); ?>" <?php echo e(request('status') == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="input-group input-group-sm">
                                    <select name="conversation_type" class="form-control">
                                        <option value="">Tüm Görüşme Türleri</option>
                                        <option value="telefon" <?php echo e(request('conversation_type') == 'telefon' ? 'selected' : ''); ?>>Telefon</option>
                                        <option value="yerinde" <?php echo e(request('conversation_type') == 'yerinde' ? 'selected' : ''); ?>>Yerinde</option>
                                        <option value="bayide" <?php echo e(request('conversation_type') == 'bayide' ? 'selected' : ''); ?>>Bayide</option>
                                    </select>
                                </div>
                                <div class="input-group input-group-sm">
                                    <input type="date" name="date_from" class="form-control" placeholder="Başlangıç" value="<?php echo e(request('date_from')); ?>">
                                </div>
                                <div class="input-group input-group-sm">
                                    <input type="date" name="date_to" class="form-control" placeholder="Bitiş" value="<?php echo e(request('date_to')); ?>">
                                </div>
                                <div class="input-group input-group-sm">
                                    <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                </div>
                                <?php if(request('status') || request('conversation_type') || request('date_from') || request('date_to')): ?>
                                    <a href="<?php echo e(route('customer-followups.all')); ?>" class="btn btn-outline-danger">Temizle</a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <table class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Müşteri</th>
                                <th>Takip Tarihi</th>
                                <th>Görüşme Tarihi</th>
                                <th>Durum</th>
                                <th>Görüşme Türü</th>
                                <th>Çalışma Türü</th>
                                <th>Ziyaret Eden</th>
                                <th>Şehir</th>
                                <th>İlçe</th>
                                <th>Not</th>
                                <th>Anlaşma</th>
                                <th>PlusCard Yükleme</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $followups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $followup): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($followup->id); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('customers.show', $followup->customer->id)); ?>" class="text-decoration-none">
                                            <?php echo e($followup->customer->company_name ?? $followup->customer->name); ?>

                                        </a>
                                    </td>
                                    <td><?php echo e(\Carbon\Carbon::parse($followup->track_date)->format('d.m.Y')); ?></td>
                                    <td><?php echo e($followup->meet_date ? \Carbon\Carbon::parse($followup->meet_date)->format('d.m.Y') : '-'); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo e($followup->status == 'aktif' ? 'success' : ($followup->status == 'pasif' ? 'warning' : 'secondary')); ?>">
                                            <?php echo e($followup->getStatusDisplayAttribute()); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?php echo e(ucfirst($followup->conversation_type)); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($followup->work_type ?? '-'); ?></td>
                                    <td><?php echo e($followup->user_name ?? '-'); ?></td>
                                    <td><?php echo e($followup->city ?? '-'); ?></td>
                                    <td><?php echo e($followup->district ?? '-'); ?></td>
                                    <td><?php echo e(Str::limit($followup->note, 30)); ?></td>
                                    <td>
                                        <?php if($followup->agreement_status === null): ?>
                                            <span class="text-muted">-</span>
                                        <?php else: ?>
                                            <span class="badge badge-<?php echo e($followup->agreement_status ? 'success' : 'danger'); ?>">
                                                <?php echo e($followup->agreement_status ? 'Evet' : 'Hayır'); ?>

                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($followup->pluscard_been_loaded === null): ?>
                                            <span class="text-muted">-</span>
                                        <?php else: ?>
                                            <span class="badge badge-<?php echo e($followup->pluscard_been_loaded ? 'success' : 'warning'); ?>">
                                                <?php echo e($followup->pluscard_been_loaded ? 'Evet' : 'Hayır'); ?>

                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('customers.customer-followups.show', [$followup->customer->id, $followup->id])); ?>" class="btn btn-info btn-sm" title="Detay">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('customers.customer-followups.edit', [$followup->customer->id, $followup->id])); ?>" class="btn btn-warning btn-sm" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('customers.customer-followups.destroy', [$followup->customer->id, $followup->id])); ?>" method="POST" style="display:inline-block;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Silmek istediğinize emin misiniz?')" title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        <?php echo e($followups->appends(request()->query())->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customer_followups/all_followups.blade.php ENDPATH**/ ?>