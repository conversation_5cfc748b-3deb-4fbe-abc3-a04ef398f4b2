@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>{{ $customer->company_name }} - <PERSON><PERSON><PERSON></h2>
        <a href="{{ route('customers.customer-followups.create', $customer->id) }}" class="btn btn-primary"><PERSON><PERSON></a>
    </div>
    @if(session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif
    <table id="customerFollowupsTable" class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th><PERSON><PERSON><PERSON></th>
                <th>Durum</th>
                <th>Not</th>
                <th><PERSON><PERSON><PERSON><PERSON></th>
                <th><PERSON>şlemler</th>
            </tr>
        </thead>
        <tbody>
            @foreach($followups as $followup)
                <tr>
                    <td>{{ $followup->id }}</td>
                    <td>{{ $followup->track_date }}</td>
                    <td>{{ $followup->status }}</td>
                    <td>{{ Str::limit($followup->note, 50) }}</td>
                    <td>{{ $followup->agreement_status === null ? '' : ($followup->agreement_status ? 'Evet' : 'Hayır') }}</td>
                    <td>
                        <a href="{{ route('customers.customer-followups.show', [$customer->id, $followup->id]) }}" class="btn btn-info btn-sm">Detay</a>
                        <a href="{{ route('customers.customer-followups.edit', [$customer->id, $followup->id]) }}" class="btn btn-warning btn-sm">Düzenle</a>
                        <form action="{{ route('customers.customer-followups.destroy', [$customer->id, $followup->id]) }}" method="POST" style="display:inline-block;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Silmek istediğinize emin misiniz?')">Sil</button>
                        </form>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{ $followups->links('pagination::bootstrap-4') }}
    <a href="{{ route('customers.index') }}" class="btn btn-secondary mt-3">Müşteri Listesine Dön</a>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#customerFollowupsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tümü"]],
        "order": [[0, "desc"]],
        "columnDefs": [
            {
                "targets": -1, // Son sütun (İşlemler)
                "orderable": false,
                "searchable": false
            }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#customerFollowupsTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
@endpush