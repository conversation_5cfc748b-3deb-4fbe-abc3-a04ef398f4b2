<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Müşteri Listesi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Müşteri Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php echo e(route('customers.create')); ?>" class="btn btn-primary">Yeni M<PERSON>şteri Ekle</a>
                        </h3>

                        <div class="card-tools">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="telefon,isim,şirket" value="<?php echo e(request('q')); ?>">

                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                    </div>
                                </div>
                                <div class="input-group input-group-sm">
                                    <span class="align-self-center" style="margin-left:8px;">Sayfa başına kayıt:</span>
                                    <select name="perPage" class="form-select" style="width: auto; max-width: 120px;" onchange="this.form.submit()">
                                        <option value="5" <?php echo e(request('perPage') == 5 ? 'selected' : ''); ?>>5</option>
                                        <option value="10" <?php echo e(request('perPage', 10) == 10 ? 'selected' : ''); ?>>10</option>
                                        <option value="25" <?php echo e(request('perPage') == 25 ? 'selected' : ''); ?>>25</option>
                                        <option value="50" <?php echo e(request('perPage') == 50 ? 'selected' : ''); ?>>50</option>
                                        <option value="100" <?php echo e(request('perPage') == 100 ? 'selected' : ''); ?>>100</option>
                                    </select>
                                </div>
                                <?php if(request('q')): ?>
                                    <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-outline-danger">Temizle</a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <table id="customersTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Email</th>
                                <th>Yetkili Unvan</th>
                                <th>Yetkili İsim</th>
                                <th>Yetkili Soyisim</th>
                                <th>Yetkili Telefon</th>
                                <th>Şirket İsmi</th>
                                <th>Şirket Telefonları</th>
                                <th>İl</th>
                                <th>İlçe</th>
                                <th>Adres</th>
                                <th>Kayıt Tarihi</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($customer->id); ?></td>
                                    <td><?php echo e($customer->email); ?></td>
                                    <td><?php echo e($customer->authorized_title); ?></td>
                                    <td><?php echo e($customer->authorized_first_name); ?></td>
                                    <td><?php echo e($customer->authorized_last_name); ?></td>
                                    <td><?php echo e($customer->authorized_phone); ?></td>
                                    <td><?php echo e($customer->company_name); ?></td>
                                    <td>
                                        <?php
                                            $allPhones = collect();

                                            // Yeni sistemden telefonları al
                                            if(method_exists($customer, 'phones') && $customer->phones) {
                                                $allPhones = $customer->phones;
                                            }

                                            // Eğer yeni sistemde telefon yoksa, eski sistemden al
                                            if($allPhones->count() == 0) {
                                                if($customer->phone_1) $allPhones->push((object)['phone' => $customer->phone_1, 'type' => 'Sabit']);
                                                if($customer->phone_2) $allPhones->push((object)['phone' => $customer->phone_2, 'type' => 'Sabit']);
                                                if($customer->phone_3) $allPhones->push((object)['phone' => $customer->phone_3, 'type' => 'Sabit']);
                                            }
                                        ?>

                                        <?php if($allPhones->count() > 0): ?>
                                            <?php $__currentLoopData = $allPhones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $phone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <small class="d-block">
                                                    <span class="badge badge-<?php echo e($phone->type == 'Mobil' ? 'success' : ($phone->type == 'Fax' ? 'warning' : 'primary')); ?> me-1"><?php echo e($phone->type); ?></span>
                                                    <?php echo e($phone->phone); ?>

                                                </small>
                                                <?php if($index < $allPhones->count() - 1): ?><br><?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <small class="text-muted">Telefon yok</small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($customer->city); ?></td>
                                    <td><?php echo e($customer->district); ?></td>
                                    <td><?php echo e($customer->address); ?></td>
                                    <td><?php echo e($customer->created_at); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('customers.show', $customer->id)); ?>" class="btn btn-info btn-sm" title="Detay">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('customers.edit', $customer->id)); ?>" class="btn btn-warning btn-sm" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo e(route('customers.customer-followups.index', $customer->id)); ?>" class="btn btn-secondary btn-sm" title="Takip Formu">
                                            <i class="fas fa-clipboard-list"></i> Takip
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        <?php echo e($customers->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#customersTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo e(route('customers.datatable')); ?>",
            "type": "GET"
        },
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "order": [[0, "desc"]],
        "columns": [
            { "data": 0, "name": "id" },
            { "data": 1, "name": "email" },
            { "data": 2, "name": "authorized_title" },
            { "data": 3, "name": "authorized_first_name" },
            { "data": 4, "name": "authorized_last_name" },
            { "data": 5, "name": "authorized_phone" },
            { "data": 6, "name": "company_name" },
            { "data": 7, "name": "phones", "orderable": false },
            { "data": 8, "name": "city" },
            { "data": 9, "name": "district" },
            { "data": 10, "name": "address" },
            { "data": 11, "name": "created_at" },
            { "data": 12, "name": "actions", "orderable": false, "searchable": false }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#customersTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customers/index.blade.php ENDPATH**/ ?>